package com.think1024.tocodesign.ideaplugin.examples

import com.intellij.openapi.project.Project
import com.think1024.tocodesign.ideaplugin.services.CodeFileService

/**
 * 方法名搜索功能使用示例
 * 演示如何使用 FindInProjectUtil 搜索方法名中的局部字符串
 */
class MethodSearchExample {

    /**
     * 搜索方法名中包含指定字符串的方法示例
     */
    fun demonstrateMethodSearch(project: Project) {
        val codeFileService = CodeFileService.getInstance(project)
        
        // 示例1: 搜索包含 "get" 的方法名
        val getMethodsResult = codeFileService.searchMethodsByNamePart(
            methodNamePart = "get",
            caseSensitive = false,
            maxResults = 10
        )
        
        println("=== 搜索包含 'get' 的方法 ===")
        println("搜索词: ${getMethodsResult.searchTerm}")
        println("找到 ${getMethodsResult.totalMatches} 个匹配的方法:")
        
        getMethodsResult.matches.forEach { match ->
            println("文件: ${match.filePath}")
            println("行号: ${match.lineNumber}")
            println("方法名: ${match.methodName}")
            println("方法签名: ${match.methodSignature}")
            println("所属类: ${match.className ?: "未知"}")
            println("代码行: ${match.lineContent}")
            println("---")
        }
        
        // 示例2: 搜索包含 "Service" 的方法名（区分大小写）
        val serviceMethodsResult = codeFileService.searchMethodsByNamePart(
            methodNamePart = "Service",
            caseSensitive = true,
            maxResults = 5
        )
        
        println("\n=== 搜索包含 'Service' 的方法（区分大小写）===")
        println("搜索词: ${serviceMethodsResult.searchTerm}")
        println("找到 ${serviceMethodsResult.totalMatches} 个匹配的方法:")
        
        serviceMethodsResult.matches.forEach { match ->
            println("${match.className}.${match.methodName} (${match.filePath}:${match.lineNumber})")
        }
        
        // 示例3: 搜索包含 "create" 的方法名
        val createMethodsResult = codeFileService.searchMethodsByNamePart(
            methodNamePart = "create",
            caseSensitive = false,
            maxResults = 15
        )
        
        println("\n=== 搜索包含 'create' 的方法 ===")
        println("找到 ${createMethodsResult.totalMatches} 个匹配的方法")
        
        // 按类名分组显示
        val methodsByClass = createMethodsResult.matches.groupBy { it.className }
        methodsByClass.forEach { (className, methods) ->
            println("\n类: ${className ?: "未知类"}")
            methods.forEach { method ->
                println("  - ${method.methodSignature} (${method.filePath}:${method.lineNumber})")
            }
        }
    }
    
    /**
     * 演示如何在实际业务中使用方法搜索
     */
    fun practicalUsageExample(project: Project) {
        val codeFileService = CodeFileService.getInstance(project)
        
        // 实际使用场景：查找所有的 getter 方法
        val getterMethods = codeFileService.searchMethodsByNamePart("get", false, 50)
        
        // 过滤出真正的 getter 方法（以 get 开头且后面跟大写字母）
        val realGetters = getterMethods.matches.filter { match ->
            match.methodName.matches(Regex("get[A-Z].*"))
        }
        
        println("=== 实际的 Getter 方法 ===")
        realGetters.forEach { getter ->
            println("${getter.className}.${getter.methodName}() - ${getter.filePath}")
        }
        
        // 查找所有的 setter 方法
        val setterMethods = codeFileService.searchMethodsByNamePart("set", false, 50)
        val realSetters = setterMethods.matches.filter { match ->
            match.methodName.matches(Regex("set[A-Z].*"))
        }
        
        println("\n=== 实际的 Setter 方法 ===")
        realSetters.forEach { setter ->
            println("${setter.className}.${setter.methodName}() - ${setter.filePath}")
        }
        
        // 查找所有的测试方法
        val testMethods = codeFileService.searchMethodsByNamePart("test", false, 30)
        val realTestMethods = testMethods.matches.filter { match ->
            match.methodName.startsWith("test") || match.lineContent.contains("@Test")
        }
        
        println("\n=== 测试方法 ===")
        realTestMethods.forEach { testMethod ->
            println("${testMethod.className}.${testMethod.methodName}() - ${testMethod.filePath}")
        }
    }
    
    /**
     * 演示搜索结果的进一步处理
     */
    fun advancedProcessingExample(project: Project) {
        val codeFileService = CodeFileService.getInstance(project)
        
        // 搜索所有包含 "process" 的方法
        val processMethodsResult = codeFileService.searchMethodsByNamePart("process", false, 100)
        
        // 统计分析
        val totalMethods = processMethodsResult.totalMatches
        val fileCount = processMethodsResult.matches.map { it.filePath }.distinct().size
        val classCount = processMethodsResult.matches.mapNotNull { it.className }.distinct().size
        
        println("=== 搜索统计 ===")
        println("搜索词: ${processMethodsResult.searchTerm}")
        println("总方法数: $totalMethods")
        println("涉及文件数: $fileCount")
        println("涉及类数: $classCount")
        
        // 按文件分组
        val methodsByFile = processMethodsResult.matches.groupBy { it.filePath }
        println("\n=== 按文件分组 ===")
        methodsByFile.forEach { (filePath, methods) ->
            println("$filePath (${methods.size} 个方法)")
            methods.forEach { method ->
                println("  第${method.lineNumber}行: ${method.methodName}")
            }
        }
        
        // 查找方法名最长的方法
        val longestMethodName = processMethodsResult.matches.maxByOrNull { it.methodName.length }
        if (longestMethodName != null) {
            println("\n=== 最长的方法名 ===")
            println("方法名: ${longestMethodName.methodName} (${longestMethodName.methodName.length} 个字符)")
            println("位置: ${longestMethodName.filePath}:${longestMethodName.lineNumber}")
        }
    }
}
