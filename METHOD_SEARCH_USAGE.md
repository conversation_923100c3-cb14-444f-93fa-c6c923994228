# 方法名搜索功能使用说明

## 概述

本功能使用 `FindInProjectUtil` 实现了对 Java 项目中方法名的局部字符串搜索。与 `TextOccurenceProcessor` 不同，这个实现能够更精确地定位到方法声明，并提供丰富的上下文信息。

## 主要功能

### `searchMethodsByNamePart` 方法

在 `CodeFileService` 中新增的核心方法，用于搜索方法名中包含指定字符串的方法。

```kotlin
fun searchMethodsByNamePart(
    methodNamePart: String,      // 要搜索的字符串片段
    caseSensitive: Boolean = false,  // 是否区分大小写
    maxResults: Int = 20         // 最大结果数量
): MethodSearchResult
```

## 返回结果

### MethodSearchResult 数据类

```kotlin
data class MethodSearchResult(
    val searchTerm: String,           // 搜索的字符串
    val totalMatches: Int,            // 总匹配数量
    val matches: List<MethodMatch>    // 匹配结果列表
)
```

### MethodMatch 数据类

```kotlin
data class MethodMatch(
    val filePath: String,             // 文件相对路径
    val lineNumber: Int,              // 行号（1基）
    val methodName: String,           // 方法名
    val methodSignature: String,      // 方法签名（包括参数）
    val lineContent: String,          // 完整的行内容
    val className: String?           // 所属类名
)
```

## 使用示例

### 基本使用

```kotlin
val codeFileService = CodeFileService.getInstance(project)

// 搜索包含 "get" 的方法名
val result = codeFileService.searchMethodsByNamePart(
    methodNamePart = "get",
    caseSensitive = false,
    maxResults = 10
)

println("找到 ${result.totalMatches} 个匹配的方法:")
result.matches.forEach { match ->
    println("${match.className}.${match.methodName} (${match.filePath}:${match.lineNumber})")
}
```

### 高级使用

```kotlin
// 查找所有 getter 方法
val getterMethods = codeFileService.searchMethodsByNamePart("get", false, 50)
val realGetters = getterMethods.matches.filter { match ->
    match.methodName.matches(Regex("get[A-Z].*"))
}

// 查找测试方法
val testMethods = codeFileService.searchMethodsByNamePart("test", false, 30)
val realTestMethods = testMethods.matches.filter { match ->
    match.methodName.startsWith("test") || match.lineContent.contains("@Test")
}

// 按类分组显示结果
val methodsByClass = result.matches.groupBy { it.className }
methodsByClass.forEach { (className, methods) ->
    println("类: ${className ?: "未知类"}")
    methods.forEach { method ->
        println("  - ${method.methodSignature}")
    }
}
```

## 技术实现

### 核心优势

1. **使用 FindInProjectUtil**: 利用 IntelliJ IDEA 原生的搜索引擎，性能更好，结果更准确
2. **PSI 树遍历**: 通过 PSI 元素向上遍历，准确识别方法声明上下文
3. **智能过滤**: 只返回真正的方法声明，过滤掉方法调用和其他匹配
4. **去重机制**: 使用唯一标识符避免重复结果
5. **丰富信息**: 提供方法签名、所属类名、行号等详细信息

### 实现流程

1. 创建 `FindModel` 配置搜索参数
2. 使用 `FindInProjectUtil.findUsages` 执行搜索
3. 遍历搜索结果，通过 PSI 树查找方法声明上下文
4. 验证方法名包含目标字符串
5. 构建 `MethodMatch` 对象并去重
6. 返回格式化的搜索结果

### 与 TextOccurenceProcessor 的区别

| 特性 | TextOccurenceProcessor | FindInProjectUtil |
|------|----------------------|-------------------|
| 搜索精度 | 文本级别，可能包含误匹配 | 语义级别，基于 PSI 树 |
| 上下文信息 | 有限 | 丰富（类名、方法签名等） |
| 性能 | 较慢 | 更快，利用索引 |
| 结果质量 | 需要额外过滤 | 直接返回准确结果 |
| 局部字符串搜索 | 支持但不够精确 | 完美支持 |

## 注意事项

1. **只支持 Java 文件**: 当前实现专门针对 Java 文件优化
2. **需要索引完成**: 在 `DumbService` 的 smart 模式下运行
3. **结果数量限制**: 默认最多返回 20 个结果，可通过参数调整
4. **大小写敏感**: 可通过 `caseSensitive` 参数控制

## 扩展可能

1. 支持其他语言（Kotlin、Scala 等）
2. 添加方法修饰符过滤（public、private 等）
3. 支持返回类型过滤
4. 添加方法参数数量过滤
5. 支持正则表达式搜索

## 性能建议

1. 合理设置 `maxResults` 参数，避免返回过多结果
2. 在大型项目中，考虑添加文件类型或包路径过滤
3. 对于频繁搜索，可以考虑添加缓存机制
