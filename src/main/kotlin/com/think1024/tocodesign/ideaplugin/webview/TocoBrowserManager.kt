package com.think1024.tocodesign.ideaplugin.webview

import com.intellij.openapi.components.Service
import com.intellij.openapi.project.Project
import com.think1024.tocodesign.ideaplugin.constants.WindowIds
import com.think1024.tocodesign.ideaplugin.settings.ApplicationPluginSettings
import java.util.concurrent.ConcurrentHashMap

@Service(Service.Level.PROJECT)
class TocoBrowserManager(private val project: Project) {
    private val browsers = ConcurrentHashMap<String, WebViewLoader>()

    @Synchronized
    fun getBrowser(id: String): WebViewLoader {
        return browsers.getOrPut(id) {
            // 获取应用设置中的 frontendHost, 如果没值，则读取host
            val host = ApplicationPluginSettings.getInstance().frontendHost.ifEmpty {
                ApplicationPluginSettings.getInstance().host
            }
            
            // 根据 ID 确定路径部分
            val path = when (id) {
                WindowIds.TOCO_MENU -> "/idea-plugin-menu"
                WindowIds.TOCO_BUILD -> "/extra-info"
                WindowIds.TOCO_DESIGN -> "/aichat-plugin"
                else -> ""
            }
            
            // 构建完整 URL
            val url = if (path.isEmpty()) {
                ""
            } else {
                // 如果是本地开发环境，使用特定端口
                if (host.contains("local.teitui.com")) {
                    "https://local.teitui.com:3000$path"
                } else {
                    "$host$path"
                }
            }

            val config = TocoBrowserConfig(isFrame = false, isOSR = true)
            if (path.isNotEmpty()) {
                config.isFrame = true
            }
//            if (id == WindowIds.TOCO_DESIGN) {
//                config.isOSR = false
//            }
            WebViewLoader(project, url, config)
        }
    }

    companion object {
        fun getInstance(project: Project): TocoBrowserManager {
            return project.getService(TocoBrowserManager::class.java)
        }
    }
}
